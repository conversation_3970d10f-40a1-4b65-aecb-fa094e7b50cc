require('dotenv').config({ path: '../.env' });
const express = require('express');
const { WebSocketServer, WebSocket } = require('ws');

const cors = require('cors');
const Anthropic = require('@anthropic-ai/sdk');

const app = express();
const PORT = process.env.BACKEND_PORT || 3000;
const WS_PORT = process.env.WEBSOCKET_PORT || 8081;

// Middleware
app.use(cors());
app.use(express.json({ limit: process.env.MAX_IMAGE_SIZE || '50mb' }));

// Global state
let deviceInfo = null;
let deviceConnection = null;
let frontendClients = new Set();
let isDiscovering = false;

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});



// WebSocket server for frontend connections
const wss = new WebSocketServer({ port: WS_PORT });

console.log(`🚀 Starting The Instrument Backend Server...`);
console.log(`📡 WebSocket server listening on port ${WS_PORT}`);
console.log(`🔌 USB Connection Mode: Requires iproxy for iOS device connectivity`);
console.log(`📱 Setup: Run 'iproxy 8082 8082' with iPhone connected via USB`);

// WebSocket connection handler for frontend clients
wss.on('connection', (ws) => {
  console.log('🔗 Frontend client connected');
  frontendClients.add(ws);
  
  // Send current device status to new client
  ws.send(JSON.stringify({
    type: 'device_status',
    connected: !!deviceConnection,
    deviceInfo: deviceInfo
  }));

  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      handleFrontendMessage(data);
    } catch (error) {
      console.error('❌ Error parsing frontend message:', error);
    }
  });

  ws.on('close', () => {
    console.log('🔌 Frontend client disconnected');
    frontendClients.delete(ws);
  });

  ws.on('error', (error) => {
    console.error('❌ Frontend WebSocket error:', error);
    frontendClients.delete(ws);
  });
});

// Handle messages from frontend
function handleFrontendMessage(data) {
  console.log('📨 Received from frontend:', data.type);
  
  if (!deviceConnection || deviceConnection.readyState !== WebSocket.OPEN) {
    console.log('⚠️ No device connection available');
    broadcastToFrontend({
      type: 'error',
      message: 'Device not connected'
    });
    return;
  }

  // Forward command to device
  deviceConnection.send(JSON.stringify(data));
}

// Broadcast message to all connected frontend clients
function broadcastToFrontend(message) {
  const messageStr = JSON.stringify(message);
  frontendClients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(messageStr);
    }
  });
}

// USB device connection function
function startDeviceConnection() {
  if (isDiscovering) return;

  isDiscovering = true;
  console.log('🔗 Starting USB device connection...');
  console.log('📱 Note: Make sure iproxy is running: iproxy 8082 8082');

  // Try direct connection to USB-forwarded port
  tryUSBConnection();
}

// Try USB connection via iproxy
function tryUSBConnection() {
  console.log('🔗 Attempting USB connection via iproxy...');

  // USB connection via iproxy forwards to localhost:8082
  const usbPort = 8082;

  tryUSBConnectionToPort(usbPort);
}

function tryUSBConnectionToPort(port) {
  console.log(`🔍 Trying USB connection to localhost:${port}`);

  // Create device info for USB connection
  deviceInfo = {
    name: 'iOS Device (USB)',
    host: 'localhost',
    port: port
  };

  // Try to connect with a callback for failure
  connectToDeviceWithCallback(() => {
    // On failure, retry after delay
    console.log(`❌ USB connection to port ${port} failed, retrying in 5 seconds...`);
    setTimeout(() => {
      if (!deviceConnection) {
        console.log('🔄 Retrying USB connection...');
        tryUSBConnection();
      }
    }, 5000);
  });
}

// Connect to discovered device
function connectToDevice() {
  connectToDeviceWithCallback(null);
}

// Connect to discovered device with failure callback
function connectToDeviceWithCallback(onFailure) {
  if (!deviceInfo || deviceConnection) return;

  // Try multiple connection strategies for iOS simulator compatibility
  const connectionStrategies = [
    `ws://${deviceInfo.host}:${deviceInfo.port}`,
    `ws://localhost:${deviceInfo.port}`,
    `ws://127.0.0.1:${deviceInfo.port}`
  ];

  tryConnectionStrategies(connectionStrategies, 0, onFailure);
}

function tryConnectionStrategies(strategies, index, onFailure) {
  if (index >= strategies.length) {
    console.error('❌ All connection strategies failed');
    deviceConnection = null;

    if (onFailure) {
      onFailure();
    } else {
      // Retry discovery after all strategies fail
      setTimeout(() => {
        if (!deviceConnection) {
          startDeviceConnection();
        }
      }, 5000);
    }
    return;
  }

  const wsUrl = strategies[index];
  console.log(`🔗 Trying connection strategy ${index + 1}/${strategies.length}: ${wsUrl}`);

  try {
    const ws = new WebSocket(wsUrl);

    // Set a connection timeout
    const connectionTimeout = setTimeout(() => {
      if (ws.readyState === WebSocket.CONNECTING) {
        console.log(`⏰ Connection timeout for ${wsUrl}, trying next strategy...`);
        ws.terminate();
        tryConnectionStrategies(strategies, index + 1);
      }
    }, 3000);

    ws.on('open', () => {
      clearTimeout(connectionTimeout);
      console.log(`✅ Connected to device at ${wsUrl}`);
      deviceConnection = ws;
      isDiscovering = false;

      broadcastToFrontend({
        type: 'device_status',
        connected: true,
        deviceInfo: { ...deviceInfo, actualHost: wsUrl }
      });
    });

    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        console.log('📨 Received from device:', data.type);

        if (data.type === 'images') {
          console.log('🖼️ Images received from device:');
          console.log(`   - Number of images: ${data.images ? data.images.length : 0}`);
          if (data.images && data.images.length > 0) {
            console.log(`   - First image size: ${data.images[0].length} characters`);
          }
        }

        // Forward device messages to frontend
        broadcastToFrontend(data);
      } catch (error) {
        console.error('❌ Error parsing device message:', error);
        console.error('❌ Raw message length:', message.length);
        console.error('❌ Raw message preview:', message.toString().substring(0, 100));
      }
    });

    ws.on('close', () => {
      clearTimeout(connectionTimeout);
      if (deviceConnection === ws) {
        console.log('🔌 Device disconnected');
        deviceConnection = null;

        broadcastToFrontend({
          type: 'device_status',
          connected: false,
          deviceInfo: null
        });

        // Attempt reconnection after delay
        setTimeout(() => {
          if (!deviceConnection) {
            console.log('🔄 Attempting to reconnect to device...');
            connectToDevice();
          }
        }, 5000);
      }
    });

    ws.on('error', (error) => {
      clearTimeout(connectionTimeout);
      if (deviceConnection === ws) {
        deviceConnection = null;
      }

      console.log(`❌ Connection failed for ${wsUrl}: ${error.message}`);

      // Try next strategy
      setTimeout(() => {
        tryConnectionStrategies(strategies, index + 1, onFailure);
      }, 100);
    });

  } catch (error) {
    console.error(`❌ Failed to create connection for ${wsUrl}:`, error);
    tryConnectionStrategies(strategies, index + 1, onFailure);
  }
}

// Routes
app.get('/api/status', (req, res) => {
  res.json({
    connected: !!deviceConnection && deviceConnection.readyState === WebSocket.OPEN,
    deviceInfo: deviceInfo,
    timestamp: new Date().toISOString()
  });
});

// LLM Chat endpoint with streaming support
app.post('/api/chat', async (req, res) => {
  try {
    const { images, context, systemPrompt, messages } = req.body;

    // Allow text-only requests (images are optional)
    const hasImages = images && Array.isArray(images) && images.length > 0;
    const hasContext = context && context.trim();

    if (!hasImages && !hasContext) {
      return res.status(400).json({ error: 'Either images or context text is required' });
    }

    console.log(`🤖 Processing request with Claude...`);
    if (hasImages) {
      console.log(`   - ${images.length} images`);
    }
    if (hasContext) {
      console.log(`   - Text context: "${context.trim().substring(0, 100)}${context.trim().length > 100 ? '...' : ''}"`);
    }
    if (systemPrompt) {
      console.log(`   - System prompt: "${systemPrompt.substring(0, 100)}${systemPrompt.length > 100 ? '...' : ''}"`);
    }
    if (messages && messages.length > 0) {
      console.log(`   - Conversation history: ${messages.length} messages`);
    }

    // Set headers for streaming
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');

    // Prepare messages for Claude
    const content = [];

    // Add context/text content
    if (hasContext) {
      if (hasImages) {
        content.push({
          type: 'text',
          text: `${context.trim()}\n\nPlease analyze the following screen capture(s) along with the above context:`
        });
      } else {
        // Text-only request
        content.push({
          type: 'text',
          text: context.trim()
        });
      }
    } else if (hasImages) {
      // Images-only request (fallback)
      content.push({
        type: 'text',
        text: 'Please analyze the following screen capture(s) and extract any relevant text, code, or visual information. Provide a clear, structured summary of what you see:'
      });
    }

    // Add images to content if present
    if (hasImages) {
      images.forEach((imageBase64, index) => {
        // Remove data URL prefix if present
        const base64Data = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');

        content.push({
          type: 'image',
          source: {
            type: 'base64',
            media_type: 'image/jpeg', // Assuming JPEG, could be made dynamic
            data: base64Data
          }
        });
      });
    }

    // Prepare messages for Claude - handle conversation history
    let claudeMessages = [];

    // Add conversation history if provided
    if (messages && Array.isArray(messages) && messages.length > 0) {
      claudeMessages = messages.map(msg => {
        // Convert assistant-ui message format to Claude format
        if (msg.role === 'user' || msg.role === 'assistant') {
          let messageContent = '';
          if (typeof msg.content === 'string') {
            messageContent = msg.content;
          } else if (Array.isArray(msg.content)) {
            // Extract text content from message parts
            messageContent = msg.content
              .filter(part => part.type === 'text')
              .map(part => part.text)
              .join('\n');
          }
          return {
            role: msg.role,
            content: messageContent
          };
        }
        return null;
      }).filter(Boolean);
    }

    // Add the current user message
    claudeMessages.push({
      role: 'user',
      content: content
    });

    // Create streaming message with Claude
    const claudeOptions = {
      model: process.env.CLAUDE_MODEL || 'claude-sonnet-4-20250514',
      max_tokens: 40000,
      messages: claudeMessages,
      stream: true
    };

    // Add system prompt if provided
    if (systemPrompt && systemPrompt.trim()) {
      claudeOptions.system = systemPrompt.trim();
    }

    const stream = await anthropic.messages.create(claudeOptions);

    // Stream the response
    for await (const chunk of stream) {
      if (chunk.type === 'content_block_delta' && chunk.delta.text) {
        const data = `data: ${JSON.stringify({ text: chunk.delta.text })}\n\n`;
        res.write(data);
      }
    }

    // End the stream
    res.write('data: [DONE]\n\n');
    res.end();

  } catch (error) {
    console.error('❌ LLM processing error:', error);

    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to process images with LLM',
        details: error.message
      });
    } else {
      res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
      res.end();
    }
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`🌐 HTTP server listening on port ${PORT}`);
  console.log(`📋 API available at http://localhost:${PORT}/api`);
  
  // Start USB device connection
  startDeviceConnection();
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down gracefully...');
  
  if (deviceConnection) {
    deviceConnection.close();
  }
  
  wss.close();
  process.exit(0);
});
